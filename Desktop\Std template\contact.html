<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - Your Company</title>
    <link rel="stylesheet" href="index.css">
    <style>
        /* Contact page specific styles */
        .contact-hero {
            background: linear-gradient(135deg, #265579 0%, #2a6592 50%, #eeb054 100%);
            color: white;
            text-align: center;
            padding: 100px 20px;
            margin-top: 0;
            position: relative;
            overflow: hidden;
        }

        .contact-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.3);
            z-index: 1;
        }

        .contact-hero-content {
            position: relative;
            z-index: 2;
        }

        .contact-hero h1 {
            font-size: 56px;
            margin-bottom: 20px;
            font-family: 'Courier New', Courier, monospace;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            animation: fadeInUp 1s ease-out;
        }

        .contact-hero p {
            font-size: 22px;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.6;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .contact-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 80px 20px;
            background: #f8f9fa;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: start;
        }

        @media (max-width: 768px) {
            .contact-grid {
                grid-template-columns: 1fr;
                gap: 40px;
            }
        }

        .contact-form-section {
            background: white;
            padding: 50px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transform: translateY(0);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .contact-form-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .contact-form-section h2 {
            font-size: 32px;
            color: #265579;
            margin-bottom: 30px;
            font-family: 'Courier New', Courier, monospace;
            text-align: center;
            position: relative;
        }

        .contact-form-section h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #eeb054, #265579);
            border-radius: 2px;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #265579;
            font-weight: bold;
            font-size: 16px;
            font-family: 'Courier New', Courier, monospace;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fff;
            box-sizing: border-box;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #eeb054;
            box-shadow: 0 0 0 3px rgba(238, 176, 84, 0.2);
            transform: translateY(-2px);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .submit-btn {
            background: linear-gradient(135deg, #265579, #eeb054);
            color: white;
            padding: 18px 40px;
            border: none;
            border-radius: 50px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            font-family: 'Courier New', Courier, monospace;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(38, 85, 121, 0.3);
            background: linear-gradient(135deg, #eeb054, #265579);
        }

        .submit-btn:active {
            transform: translateY(-1px);
        }

        .contact-info-section {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .contact-info-card {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .contact-info-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            border-color: #eeb054;
        }

        .contact-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #265579, #eeb054);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            font-size: 35px;
            color: white;
            transition: all 0.3s ease;
        }

        .contact-info-card:hover .contact-icon {
            transform: rotate(360deg) scale(1.1);
        }

        .contact-info-card h3 {
            font-size: 24px;
            color: #265579;
            margin-bottom: 15px;
            font-family: 'Courier New', Courier, monospace;
        }

        .contact-info-card p {
            font-size: 16px;
            color: #666;
            line-height: 1.6;
            margin: 0;
        }

        .contact-info-card a {
            color: #eeb054;
            text-decoration: none;
            font-weight: bold;
            transition: color 0.3s ease;
        }

        .contact-info-card a:hover {
            color: #265579;
        }

        .map-section {
            margin-top: 60px;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .map-header {
            background: linear-gradient(135deg, #265579, #eeb054);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .map-header h3 {
            font-size: 28px;
            margin: 0;
            font-family: 'Courier New', Courier, monospace;
        }

        .map-placeholder {
            height: 300px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #666;
            font-family: 'Courier New', Courier, monospace;
        }

        .business-hours {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            margin-top: 30px;
        }

        .business-hours h3 {
            font-size: 24px;
            color: #265579;
            margin-bottom: 25px;
            font-family: 'Courier New', Courier, monospace;
            text-align: center;
        }

        .hours-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .hours-item {
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }

        .hours-item:last-child {
            border-bottom: none;
        }

        .day {
            font-weight: bold;
            color: #265579;
        }

        .time {
            color: #eeb054;
            font-weight: bold;
        }

        .cta-section {
            background: linear-gradient(135deg, #265579, #2a6592);
            color: white;
            padding: 60px 20px;
            text-align: center;
            margin-top: 60px;
        }

        .cta-section h2 {
            font-size: 36px;
            margin-bottom: 20px;
            font-family: 'Courier New', Courier, monospace;
            color: #eeb054;
        }

        .cta-section p {
            font-size: 18px;
            margin-bottom: 30px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .cta-btn {
            background: #eeb054;
            color: #265579;
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-family: 'Courier New', Courier, monospace;
        }

        .cta-btn:hover {
            background: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">Your Company</div>
        <div class="header-keywords">
            <span><a href="index.html">Home</a></span>
            <span><a href="about.html">About</a></span>
            <span><a href="contact.html">Contact</a></span>
            <span><a href="services.html">Services</a></span>
        </div>
    </div>

    <!-- Hero Section -->
    <div class="contact-hero">
        <div class="contact-hero-content">
            <h1>Get In Touch</h1>
            <p>We'd love to hear from you! Whether you have a question about our products, need support, or want to discuss a potential partnership, our team is ready to help.</p>
        </div>
    </div>

    <!-- Main Contact Section -->
    <div class="contact-container">
        <div class="contact-grid">
            <!-- Contact Form -->
            <div class="contact-form-section">
                <h2>Send Us a Message</h2>
                <form action="#" method="POST">
                    <div class="form-group">
                        <label for="name">Full Name *</label>
                        <input type="text" id="name" name="name" required placeholder="Enter your full name">
                    </div>

                    <div class="form-group">
                        <label for="email">Email Address *</label>
                        <input type="email" id="email" name="email" required placeholder="Enter your email address">
                    </div>

                    <div class="form-group">
                        <label for="phone">Phone Number</label>
                        <input type="tel" id="phone" name="phone" placeholder="Enter your phone number">
                    </div>

                    <div class="form-group">
                        <label for="subject">Subject *</label>
                        <select id="subject" name="subject" required>
                            <option value="">Select a subject</option>
                            <option value="general">General Inquiry</option>
                            <option value="support">Technical Support</option>
                            <option value="sales">Sales Question</option>
                            <option value="partnership">Partnership</option>
                            <option value="feedback">Feedback</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="message">Message *</label>
                        <textarea id="message" name="message" required placeholder="Tell us how we can help you..."></textarea>
                    </div>

                    <button type="submit" class="submit-btn">Send Message</button>
                </form>
            </div>

            <!-- Contact Information -->
            <div class="contact-info-section">
                <div class="contact-info-card">
                    <div class="contact-icon">📍</div>
                    <h3>Visit Our Office</h3>
                    <p>123 Business Street<br>
                    Suite 100<br>
                    City, State 12345<br>
                    United States</p>
                </div>

                <div class="contact-info-card">
                    <div class="contact-icon">📞</div>
                    <h3>Call Us</h3>
                    <p>Main: <a href="tel:+***********">(*************</a><br>
                    Support: <a href="tel:+***********">(*************</a><br>
                    Toll Free: <a href="tel:+***********">(*************</a></p>
                </div>

                <div class="contact-info-card">
                    <div class="contact-icon">✉️</div>
                    <h3>Email Us</h3>
                    <p>General: <a href="mailto:<EMAIL>"><EMAIL></a><br>
                    Support: <a href="mailto:<EMAIL>"><EMAIL></a><br>
                    Sales: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>

                <div class="business-hours">
                    <h3>Business Hours</h3>
                    <div class="hours-grid">
                        <div class="hours-item">
                            <span class="day">Monday</span>
                            <span class="time">9:00 AM - 6:00 PM</span>
                        </div>
                        <div class="hours-item">
                            <span class="day">Tuesday</span>
                            <span class="time">9:00 AM - 6:00 PM</span>
                        </div>
                        <div class="hours-item">
                            <span class="day">Wednesday</span>
                            <span class="time">9:00 AM - 6:00 PM</span>
                        </div>
                        <div class="hours-item">
                            <span class="day">Thursday</span>
                            <span class="time">9:00 AM - 6:00 PM</span>
                        </div>
                        <div class="hours-item">
                            <span class="day">Friday</span>
                            <span class="time">9:00 AM - 5:00 PM</span>
                        </div>
                        <div class="hours-item">
                            <span class="day">Saturday</span>
                            <span class="time">10:00 AM - 2:00 PM</span>
                        </div>
                        <div class="hours-item">
                            <span class="day">Sunday</span>
                            <span class="time">Closed</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Map Section -->
        <div class="map-section">
            <div class="map-header">
                <h3>Find Us on the Map</h3>
            </div>
            <div class="map-placeholder">
                🗺️ Interactive Map Coming Soon - 123 Business Street, City, State 12345
            </div>
        </div>
    </div>

    <!-- Call to Action Section -->
    <div class="cta-section">
        <h2>Ready to Get Started?</h2>
        <p>Don't wait! Contact us today and let's discuss how we can help you achieve your goals. Our team is standing by to provide you with the best solutions.</p>
        <div class="cta-buttons">
            <a href="tel:+***********" class="cta-btn">📞 Call Now</a>
            <a href="mailto:<EMAIL>" class="cta-btn">✉️ Email Us</a>
            <a href="#" class="cta-btn">💬 Live Chat</a>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer1">
        <h3>Your Company</h3>
        <h3>123 Business Street, City, State 12345</h3>
        <h3>Phone: (*************</h3>
        <h3>Email: <EMAIL></h3>
        <h3>Website: www.yourcompany.com</h3>
        <div class="footer2">
            <p>We are committed to providing exceptional customer service and support. Our team is available during business hours to assist you with any questions or concerns. Contact us today to experience the difference our personalized approach can make for your business.</p>
        </div>
        <div class="footer3">
            <h3>Follow us on</h3>
            <a href="https://www.facebook.com"><img src="#" alt="facebook" class="footer3"></a>
            <a href="https://www.twitter.com"><img src="#" alt="twitter" class="footer3"></a>
            <a href="https://www.instagram.com"><img src="#" alt="instagram" class="footer3"></a>
        </div>
        <h3 style="text-align: center;">@copyright by webnexis at 2024 📲 6383097069</h3>
    </div>

</body>
</html>