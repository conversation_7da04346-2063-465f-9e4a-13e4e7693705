*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.header{
    display: flex;
    justify-content: space-between; /* This pushes company name left and keywords right */
    align-items: center;
    background-color: #333;
    color: #fff;
    padding: 10px;
    font-size: 20px;
    font-weight: bold;
    margin-top: 0;
    font-family: 'Courier New', Courier, monospace;
}

.header-keywords span a {
    margin-left: 20px; /* Space between keywords */
    font-weight: bold;
    font-size: 18px;
    cursor: pointer;
    text-decoration: none;
    color: #eeb054;
    transition: color 0.3s;
    font-family: 'Courier New', Courier, monospace;
    text-decoration: none;
}

.content h1 {
    font-size: 26px;
    margin: 20px 0;
    margin-top: 100px; /* Adjusted to avoid overlap with header */
    
}

.content p {
    font-size: 24px;
    line-height: 1.5;
    margin: 10px 0;
    display: inline-block;
    margin-top: 120;
}