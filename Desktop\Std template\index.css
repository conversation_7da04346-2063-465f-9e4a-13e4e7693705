.header{
    display: flex;
    justify-content: space-between; /* Changed from space-evenly */
    align-items: center;
    background-color: #333;
    color: #fff;
    padding: 10px;
    font-size: 20px;
    font-weight: bold;
}

.company-name {
    /* Left side styles (optional) */
}

.header-keywords span {
    margin-left: 20px; /* Space between keywords */
    font-weight: normal;
    font-size: 18px;
    cursor: pointer;
    text-decoration: none;
    color: #fff;
    transition: color 0.3s;
    justify-content: center;
}