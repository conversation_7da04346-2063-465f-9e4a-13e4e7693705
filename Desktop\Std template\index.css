*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.header{
    display: flex;
    justify-content: space-between; /* This pushes company name left and keywords right */
    align-items: center;
    background-color: #333;
    color: #fff;
    padding: 10px;
    font-size: 20px;
    font-weight: bold;
    margin-top: 0;
    font-family: 'Courier New', Courier, monospace;
}

.header-keywords span a {
    margin-left: 20px; /* Space between keywords */
    font-weight: bold;
    font-size: 18px;
    cursor: pointer;
    text-decoration: none;
    color: #eeb054;
    transition: color 0.3s;
    font-family: 'Courier New', Courier, monospace;
    text-decoration: none;
}

.content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 50px 20px;
    margin-top: 50px;
}

.content-text {
    flex: 1;
    padding-right: 40px;
}

.placeholder-image {
    flex: 1;
    text-align: center;
    margin-top: 0;
    border-radius: 15px;
}

.product {
    height: 440px;
    width: 350px;
    font-family: <PERSON><PERSON>, "Helvetica Neue", Helvetica, sans-serif;
    font-weight: 100;
    margin-left: 4%;
    margin-right: 3%;
    border: solid 2px;
    border-radius: 15px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    margin-top: 8%;
}


/* From Uiverse.io by iZOXVL  for button*/ 
.boton-elegante {
  padding: 15px 30px;
  border: 2px solid #2c2c2c;
  background-color: #1a1a1a;
  color: #ffffff;
  font-size: 1.2rem;
  cursor: pointer;
  border-radius: 30px;
  transition: all 0.4s ease;
  outline: none;
  position: relative;
  overflow: hidden;
  font-weight: bold;
}

.boton-elegante::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  transform: scale(0);
  transition: transform 0.5s ease;
}

.boton-elegante:hover::after {
  transform: scale(4);
}

.boton-elegante:hover {
  border-color: #666666;
  background: #292929;
}


/* Footer Styles */
.footer1 {
    background-color: #265579;
    color: #fff;
    padding: 40px 20px 20px 20px;
    margin-top: 50px;
    font-family: 'Courier New', Courier, monospace;
}

.footer1 h3 {
    color: #eeb054;
    font-size: 18px;
    margin: 10px 0;
    font-weight: bold;
}

.footer2 {
    margin: 20px 0;
    padding: 15px 0;
    border-top: 1px solid #555;
    border-bottom: 1px solid #555;
}

.footer2 p {
    font-size: 14px;
    line-height: 1.6;
    color: #ccc;
    margin: 0;
}

.footer3 {
    margin-top: 20px;
    text-align: center;
}

.footer3 h3 {
    margin-bottom: 15px;
    color: #eeb054;
}

.footer3 a {
    margin: 0 10px;
    display: inline-block;
    text-decoration: none;
    color: #cddde9;
}

.footer3 img {
    width: 30px;
    height: 30px;
    transition: transform 0.3s ease;
}

.footer3 img:hover {
    transform: scale(1.1);
}

/* Horizontal rule styling */
hr {
    border: none;
    height: 2px;
    background-color: #eeb054;
    margin: 30px 0 0 0;
}