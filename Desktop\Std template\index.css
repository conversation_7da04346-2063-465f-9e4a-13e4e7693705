*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.header{
    display: flex;
    justify-content: space-between; /* This pushes company name left and keywords right */
    align-items: center;
    background-color: #333;
    color: #fff;
    padding: 10px;
    font-size: 20px;
    font-weight: bold;
    margin-top: 0;
    font-family: 'Courier New', Courier, monospace;
}

.header-keywords span a {
    margin-left: 20px; /* Space between keywords */
    font-weight: bold;
    font-size: 18px;
    cursor: pointer;
    text-decoration: none;
    color: #eeb054;
    transition: color 0.3s;
    font-family: 'Courier New', Courier, monospace;
    text-decoration: none;
}

.content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 50px 20px;
    margin-top: 50px;
}

.content-text {
    flex: 1;
    padding-right: 40px;
}

.placeholder-image {
    flex: 1;
    text-align: center;
    margin-top: 0;
    border-radius: 15px;
}