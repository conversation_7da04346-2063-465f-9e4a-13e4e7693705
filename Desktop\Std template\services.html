<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Services - Your Company</title>
    <link rel="stylesheet" href="index.css">
    <style>
        /* Services page specific styles */
        .services-hero {
            background: linear-gradient(135deg, #265579 0%, #2a6592 30%, #eeb054 70%, #f4c76d 100%);
            color: white;
            text-align: center;
            padding: 120px 20px;
            margin-top: 0;
            position: relative;
            overflow: hidden;
        }

        .services-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="90" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(0px) translateX(0px); }
            50% { transform: translateY(-20px) translateX(10px); }
            100% { transform: translateY(0px) translateX(0px); }
        }

        .services-hero-content {
            position: relative;
            z-index: 2;
        }

        .services-hero h1 {
            font-size: 64px;
            margin-bottom: 25px;
            font-family: 'Courier New', Courier, monospace;
            color: #fff;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            animation: slideInDown 1s ease-out;
        }

        .services-hero p {
            font-size: 24px;
            max-width: 800px;
            margin: 0 auto 40px;
            line-height: 1.6;
            animation: slideInUp 1s ease-out 0.3s both;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero-stats {
            display: flex;
            justify-content: center;
            gap: 60px;
            margin-top: 50px;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
            animation: fadeIn 1s ease-out 0.6s both;
        }

        .stat-number {
            font-size: 48px;
            font-weight: bold;
            color: #eeb054;
            display: block;
            font-family: 'Courier New', Courier, monospace;
        }

        .stat-label {
            font-size: 16px;
            margin-top: 5px;
            opacity: 0.9;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .services-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 100px 20px;
        }

        .section-title {
            text-align: center;
            margin-bottom: 80px;
        }

        .section-title h2 {
            font-size: 48px;
            color: #265579;
            margin-bottom: 20px;
            font-family: 'Courier New', Courier, monospace;
            position: relative;
        }

        .section-title h2::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, #eeb054, #265579);
            border-radius: 2px;
        }

        .section-title p {
            font-size: 20px;
            color: #666;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            margin-bottom: 100px;
        }

        .service-card {
            background: white;
            border-radius: 25px;
            padding: 50px 40px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            border: 3px solid transparent;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(238, 176, 84, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .service-card:hover::before {
            left: 100%;
        }

        .service-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 30px 80px rgba(0,0,0,0.15);
            border-color: #eeb054;
        }

        .service-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #265579, #eeb054);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 45px;
            color: white;
            transition: all 0.4s ease;
            position: relative;
            z-index: 2;
        }

        .service-card:hover .service-icon {
            transform: rotateY(360deg) scale(1.1);
            background: linear-gradient(135deg, #eeb054, #265579);
        }

        .service-card h3 {
            font-size: 28px;
            color: #265579;
            margin-bottom: 20px;
            font-family: 'Courier New', Courier, monospace;
            position: relative;
            z-index: 2;
        }

        .service-card p {
            font-size: 16px;
            color: #666;
            line-height: 1.8;
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }

        .service-features {
            list-style: none;
            padding: 0;
            margin: 30px 0;
            position: relative;
            z-index: 2;
        }

        .service-features li {
            padding: 8px 0;
            color: #555;
            position: relative;
            padding-left: 25px;
        }

        .service-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #eeb054;
            font-weight: bold;
            font-size: 16px;
        }

        .service-btn {
            background: linear-gradient(135deg, #265579, #eeb054);
            color: white;
            padding: 15px 35px;
            border: none;
            border-radius: 50px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-family: 'Courier New', Courier, monospace;
            position: relative;
            z-index: 2;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .service-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(38, 85, 121, 0.3);
            background: linear-gradient(135deg, #eeb054, #265579);
        }

        /* Pricing Section */
        .pricing-section {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 100px 20px;
            margin-top: 50px;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .pricing-card {
            background: white;
            border-radius: 25px;
            padding: 50px 40px;
            text-align: center;
            box-shadow: 0 25px 70px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            border: 3px solid transparent;
        }

        .pricing-card.featured {
            transform: scale(1.05);
            border-color: #eeb054;
            background: linear-gradient(135deg, #fff, #fffbf5);
        }

        .pricing-card.featured::before {
            content: 'MOST POPULAR';
            position: absolute;
            top: 20px;
            right: -30px;
            background: #eeb054;
            color: white;
            padding: 8px 40px;
            font-size: 12px;
            font-weight: bold;
            transform: rotate(45deg);
            letter-spacing: 1px;
        }

        .pricing-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 35px 90px rgba(0,0,0,0.15);
        }

        .pricing-card.featured:hover {
            transform: translateY(-10px) scale(1.05);
        }

        .pricing-header h3 {
            font-size: 32px;
            color: #265579;
            margin-bottom: 15px;
            font-family: 'Courier New', Courier, monospace;
        }

        .pricing-price {
            font-size: 64px;
            color: #eeb054;
            font-weight: bold;
            margin: 30px 0;
            font-family: 'Courier New', Courier, monospace;
        }

        .pricing-price span {
            font-size: 20px;
            color: #666;
        }

        .pricing-features {
            list-style: none;
            padding: 0;
            margin: 40px 0;
        }

        .pricing-features li {
            padding: 12px 0;
            color: #555;
            border-bottom: 1px solid #f0f0f0;
            position: relative;
            padding-left: 30px;
        }

        .pricing-features li::before {
            content: '🚀';
            position: absolute;
            left: 0;
            font-size: 16px;
        }

        .pricing-btn {
            background: linear-gradient(135deg, #265579, #eeb054);
            color: white;
            padding: 18px 40px;
            border: none;
            border-radius: 50px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            font-family: 'Courier New', Courier, monospace;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .pricing-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(38, 85, 121, 0.3);
            background: linear-gradient(135deg, #eeb054, #265579);
        }

        /* Process Timeline */
        .process-section {
            background: white;
            padding: 100px 20px;
        }

        .process-timeline {
            max-width: 1000px;
            margin: 0 auto;
            position: relative;
        }

        .process-timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(180deg, #265579, #eeb054);
            transform: translateX(-50%);
        }

        .process-step {
            display: flex;
            align-items: center;
            margin-bottom: 80px;
            position: relative;
        }

        .process-step:nth-child(even) {
            flex-direction: row-reverse;
        }

        .process-content {
            flex: 1;
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 50px rgba(0,0,0,0.1);
            margin: 0 40px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .process-content:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 60px rgba(0,0,0,0.15);
            border-color: #eeb054;
        }

        .process-number {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #265579, #eeb054);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: bold;
            color: white;
            font-family: 'Courier New', Courier, monospace;
            z-index: 2;
        }

        .process-content h3 {
            font-size: 28px;
            color: #265579;
            margin-bottom: 15px;
            font-family: 'Courier New', Courier, monospace;
        }

        .process-content p {
            font-size: 16px;
            color: #666;
            line-height: 1.8;
        }

        /* Testimonials */
        .testimonials-section {
            background: linear-gradient(135deg, #265579, #2a6592);
            color: white;
            padding: 100px 20px;
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .testimonial-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .testimonial-card:hover {
            transform: translateY(-10px);
            background: rgba(255,255,255,0.15);
        }

        .testimonial-text {
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 30px;
            font-style: italic;
        }

        .testimonial-author {
            font-size: 16px;
            font-weight: bold;
            color: #eeb054;
        }

        .testimonial-role {
            font-size: 14px;
            opacity: 0.8;
            margin-top: 5px;
        }

        /* CTA Section */
        .cta-section {
            background: linear-gradient(135deg, #eeb054, #f4c76d);
            color: #265579;
            padding: 80px 20px;
            text-align: center;
        }

        .cta-content h2 {
            font-size: 48px;
            margin-bottom: 20px;
            font-family: 'Courier New', Courier, monospace;
        }

        .cta-content p {
            font-size: 20px;
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .cta-btn {
            background: #265579;
            color: white;
            padding: 20px 40px;
            border: none;
            border-radius: 50px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-family: 'Courier New', Courier, monospace;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .cta-btn:hover {
            background: #1a3d52;
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(26, 61, 82, 0.3);
        }

        .cta-btn.secondary {
            background: transparent;
            border: 3px solid #265579;
            color: #265579;
        }

        .cta-btn.secondary:hover {
            background: #265579;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">Your Company</div>
        <div class="header-keywords">
            <span><a href="index.html">Home</a></span>
            <span><a href="about.html">About</a></span>
            <span><a href="contact.html">Contact</a></span>
            <span><a href="services.html">Services</a></span>
        </div>
    </div>

    <!-- Hero Section -->
    <div class="services-hero">
        <div class="services-hero-content">
            <h1>Our Services</h1>
            <p>Discover our comprehensive range of professional services designed to help your business grow, succeed, and thrive in today's competitive market.</p>

            <div class="hero-stats">
                <div class="stat-item">
                    <span class="stat-number">500+</span>
                    <div class="stat-label">Projects Completed</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">98%</span>
                    <div class="stat-label">Client Satisfaction</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">24/7</span>
                    <div class="stat-label">Support Available</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">4+</span>
                    <div class="stat-label">Years Experience</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Services Section -->
    <div class="services-container">
        <div class="section-title">
            <h2>What We Offer</h2>
            <p>Our comprehensive suite of services is designed to meet all your business needs with excellence and innovation.</p>
        </div>

        <div class="services-grid">
            <div class="service-card">
                <div class="service-icon">🚀</div>
                <h3>Web Development</h3>
                <p>Create stunning, responsive websites that engage your audience and drive business growth with cutting-edge technology.</p>
                <ul class="service-features">
                    <li>Responsive Design</li>
                    <li>SEO Optimization</li>
                    <li>Fast Loading Speed</li>
                    <li>Mobile-First Approach</li>
                    <li>Cross-Browser Compatible</li>
                </ul>
                <a href="#" class="service-btn">Learn More</a>
            </div>

            <div class="service-card">
                <div class="service-icon">📱</div>
                <h3>Mobile App Development</h3>
                <p>Build powerful mobile applications for iOS and Android that provide exceptional user experiences and functionality.</p>
                <ul class="service-features">
                    <li>Native & Hybrid Apps</li>
                    <li>UI/UX Design</li>
                    <li>App Store Optimization</li>
                    <li>Push Notifications</li>
                    <li>Analytics Integration</li>
                </ul>
                <a href="#" class="service-btn">Learn More</a>
            </div>

            <div class="service-card">
                <div class="service-icon">🎨</div>
                <h3>UI/UX Design</h3>
                <p>Design beautiful, intuitive interfaces that enhance user experience and create lasting impressions for your brand.</p>
                <ul class="service-features">
                    <li>User Research</li>
                    <li>Wireframing</li>
                    <li>Prototyping</li>
                    <li>Visual Design</li>
                    <li>Usability Testing</li>
                </ul>
                <a href="#" class="service-btn">Learn More</a>
            </div>

            <div class="service-card">
                <div class="service-icon">📊</div>
                <h3>Digital Marketing</h3>
                <p>Boost your online presence and reach your target audience with strategic digital marketing campaigns and SEO.</p>
                <ul class="service-features">
                    <li>SEO & SEM</li>
                    <li>Social Media Marketing</li>
                    <li>Content Marketing</li>
                    <li>Email Campaigns</li>
                    <li>Analytics & Reporting</li>
                </ul>
                <a href="#" class="service-btn">Learn More</a>
            </div>

            <div class="service-card">
                <div class="service-icon">☁️</div>
                <h3>Cloud Solutions</h3>
                <p>Migrate to the cloud and optimize your infrastructure for better performance, security, and scalability.</p>
                <ul class="service-features">
                    <li>Cloud Migration</li>
                    <li>Infrastructure Setup</li>
                    <li>Security Implementation</li>
                    <li>Backup Solutions</li>
                    <li>24/7 Monitoring</li>
                </ul>
                <a href="#" class="service-btn">Learn More</a>
            </div>

            <div class="service-card">
                <div class="service-icon">🔧</div>
                <h3>Technical Support</h3>
                <p>Get reliable technical support and maintenance services to keep your systems running smoothly and efficiently.</p>
                <ul class="service-features">
                    <li>24/7 Support</li>
                    <li>System Maintenance</li>
                    <li>Bug Fixes</li>
                    <li>Performance Optimization</li>
                    <li>Security Updates</li>
                </ul>
                <a href="#" class="service-btn">Learn More</a>
            </div>
        </div>
    </div>

    <!-- Pricing Section -->
    <div class="pricing-section">
        <div class="section-title">
            <h2>Our Pricing Plans</h2>
            <p>Choose the perfect plan that fits your needs and budget. All plans include our premium support and satisfaction guarantee.</p>
        </div>

        <div class="pricing-grid">
            <div class="pricing-card">
                <div class="pricing-header">
                    <h3>Starter</h3>
                    <div class="pricing-price">$299<span>/month</span></div>
                </div>
                <ul class="pricing-features">
                    <li>Basic Website Design</li>
                    <li>5 Pages Included</li>
                    <li>Mobile Responsive</li>
                    <li>Basic SEO Setup</li>
                    <li>Email Support</li>
                    <li>1 Month Free Support</li>
                </ul>
                <button class="pricing-btn">Get Started</button>
            </div>

            <div class="pricing-card featured">
                <div class="pricing-header">
                    <h3>Professional</h3>
                    <div class="pricing-price">$599<span>/month</span></div>
                </div>
                <ul class="pricing-features">
                    <li>Custom Website Design</li>
                    <li>15 Pages Included</li>
                    <li>Advanced SEO</li>
                    <li>Social Media Integration</li>
                    <li>Analytics Setup</li>
                    <li>Priority Support</li>
                    <li>3 Months Free Support</li>
                </ul>
                <button class="pricing-btn">Get Started</button>
            </div>

            <div class="pricing-card">
                <div class="pricing-header">
                    <h3>Enterprise</h3>
                    <div class="pricing-price">$999<span>/month</span></div>
                </div>
                <ul class="pricing-features">
                    <li>Premium Custom Design</li>
                    <li>Unlimited Pages</li>
                    <li>E-commerce Integration</li>
                    <li>Advanced Analytics</li>
                    <li>24/7 Phone Support</li>
                    <li>Dedicated Account Manager</li>
                    <li>6 Months Free Support</li>
                </ul>
                <button class="pricing-btn">Get Started</button>
            </div>
        </div>
    </div>

    <!-- Process Timeline -->
    <div class="process-section">
        <div class="section-title">
            <h2>Our Process</h2>
            <p>We follow a proven methodology to ensure your project is delivered on time, within budget, and exceeds expectations.</p>
        </div>

        <div class="process-timeline">
            <div class="process-step">
                <div class="process-number">1</div>
                <div class="process-content">
                    <h3>Discovery & Planning</h3>
                    <p>We start by understanding your business goals, target audience, and project requirements. Our team conducts thorough research and creates a detailed project plan with timelines and milestones.</p>
                </div>
            </div>

            <div class="process-step">
                <div class="process-number">2</div>
                <div class="process-content">
                    <h3>Design & Prototyping</h3>
                    <p>Our designers create wireframes and prototypes that bring your vision to life. We focus on user experience and ensure the design aligns with your brand identity and business objectives.</p>
                </div>
            </div>

            <div class="process-step">
                <div class="process-number">3</div>
                <div class="process-content">
                    <h3>Development & Testing</h3>
                    <p>Our developers build your solution using the latest technologies and best practices. We conduct rigorous testing to ensure everything works perfectly across all devices and browsers.</p>
                </div>
            </div>

            <div class="process-step">
                <div class="process-number">4</div>
                <div class="process-content">
                    <h3>Launch & Support</h3>
                    <p>We deploy your project and provide comprehensive training. Our ongoing support ensures your solution continues to perform optimally and evolves with your business needs.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Testimonials Section -->
    <div class="testimonials-section">
        <div class="section-title">
            <h2 style="color: #eeb054;">What Our Clients Say</h2>
            <p style="color: rgba(255,255,255,0.9);">Don't just take our word for it. Here's what our satisfied clients have to say about our services.</p>
        </div>

        <div class="testimonials-grid">
            <div class="testimonial-card">
                <div class="testimonial-text">
                    "Working with this team was an absolute pleasure. They delivered our website ahead of schedule and exceeded all our expectations. The design is beautiful and the functionality is flawless."
                </div>
                <div class="testimonial-author">Sarah Johnson</div>
                <div class="testimonial-role">CEO, TechStart Inc.</div>
            </div>

            <div class="testimonial-card">
                <div class="testimonial-text">
                    "Their mobile app development service transformed our business. The app is user-friendly, fast, and has significantly increased our customer engagement. Highly recommended!"
                </div>
                <div class="testimonial-author">Michael Chen</div>
                <div class="testimonial-role">Founder, RetailPro</div>
            </div>

            <div class="testimonial-card">
                <div class="testimonial-text">
                    "The digital marketing campaign they created for us resulted in a 300% increase in online sales. Their expertise and dedication to our success is unmatched."
                </div>
                <div class="testimonial-author">Emily Rodriguez</div>
                <div class="testimonial-role">Marketing Director, GrowthCorp</div>
            </div>
        </div>
    </div>

    <!-- Call to Action Section -->
    <div class="cta-section">
        <div class="cta-content">
            <h2>Ready to Get Started?</h2>
            <p>Let's discuss your project and see how we can help you achieve your business goals. Contact us today for a free consultation!</p>
            <div class="cta-buttons">
                <a href="contact.html" class="cta-btn">Get Free Quote</a>
                <a href="tel:+***********" class="cta-btn secondary">Call Us Now</a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer1">
        <h3>Your Company</h3>
        <h3>123 Business Street, City, State 12345</h3>
        <h3>Phone: (*************</h3>
        <h3>Email: <EMAIL></h3>
        <h3>Website: www.yourcompany.com</h3>
        <div class="footer2">
            <p>We are committed to delivering exceptional services that drive business growth and success. Our team of experts is dedicated to providing innovative solutions that meet your unique needs and exceed your expectations.</p>
        </div>
        <div class="footer3">
            <h3>Follow us on</h3>
            <a href="https://www.facebook.com"><img src="#" alt="facebook" class="footer3"></a>
            <a href="https://www.twitter.com"><img src="#" alt="twitter" class="footer3"></a>
            <a href="https://www.instagram.com"><img src="#" alt="instagram" class="footer3"></a>
        </div>
        <h3 style="text-align: center;">@copyright by webnexis at 2024 📲 6383097069</h3>
    </div>

</body>
</html>